import asyncio
import pandas as pd
import time
import logging
import json
from datetime import datetime, timedelta, timezone

from app.exchange.kraken_connection import KrakenConnection
from app.exchange.kraken_websocket import ChannelType
from app.exchange.kraken_api import KrakenAPI
from app.strategy.base_strategy import BaseStrategy
from app.db.db_executor import DatabaseExecutor
from app.scripts.base_data_feed import DataFeed

logger = logging.getLogger(__name__)

INTERVAL_MAPPING = {
    "min": 1,
    "h": 60,
    "D": 1440,    # 24 * 60
    "W": 10080,   # 7 * 1440
    "M": 43200,   # 30 * 1440 (approximate month)
}

# Mapping of interval minutes to Kraken v2 interval strings
KRAKEN_V2_INTERVALS = {
    1: "1m",      # 1 minute
    5: "5m",      # 5 minutes
    15: "15m",    # 15 minutes
    30: "30m",    # 30 minutes
    60: "1h",     # 1 hour
    240: "4h",    # 4 hours
    1440: "1d",   # 1 day
    10080: "1w",  # 1 week
    21600: "1M"   # 1 month (approximate)
}


class KrakenDataFeed(DataFeed):
    """
    Handles the business logic for processing Kraken market data.
    Uses KrakenConnection for WebSocket communication.
    """

    def __init__(self, pair: str, interval: str, api: KrakenAPI,
                 db: DatabaseExecutor, strategy: BaseStrategy,
                 book_depth: int = 10, max_queue_size: int = 100):
        """
        Initialize the Kraken data feed.

        Args:
            pair: Trading pair (e.g., 'XBT/USD')
            interval: Candle interval (e.g., '1h', '4h', '1d')
            api: KrakenAPI instance for REST calls
            db: Database executor for storing data
            strategy: Trading strategy implementation
            book_depth: Depth of the order book to maintain
            max_queue_size: Maximum number of candles to keep in memory
        """
        self.pair = pair
        self.interval = interval
        self.api = api
        self.db = db
        self.strategy = strategy
        self.book_depth = book_depth
        self.max_queue_size = max_queue_size

        # Initialize connection manager
        self.connection = KrakenConnection(target_symbol=pair)

        # Data structures
        self.candle_queue = asyncio.Queue(maxsize=max_queue_size)
        self.order_book = {'bids': {}, 'asks': {}}
        self._processing_lock = asyncio.Lock()

        # Parse interval
        self.interval_minutes = self._parse_interval(interval)
        self.kraken_interval = KRAKEN_V2_INTERVALS.get(
            self.interval_minutes, "1h")
        logger.info(f"Using interval: {self.kraken_interval}")

    def _parse_interval(self, interval: str) -> int:
        """Parse interval string into minutes"""
        if isinstance(interval, int):
            return interval

        # Extract unit (e.g., 'min', 'h')
        unit = ''.join(filter(str.isalpha, interval))
        value_str = ''.join(filter(str.isdigit, interval))

        if not value_str or not unit:
            raise ValueError(f"Invalid interval format: {interval}")

        value = int(value_str)

        if unit not in INTERVAL_MAPPING:
            raise ValueError(f"Invalid unit in interval: {unit}")
        return value * INTERVAL_MAPPING[unit]  # Convert to minutes

    async def connect(self):
        """Establish connection and initialize subscriptions"""
        await self.connection.connect()
        await self.initialize_historical_data()
        await self._initialize_subscriptions()

    async def disconnect(self):
        """Close the connection"""
        await self.connection.disconnect()

    async def _initialize_subscriptions(self):
        """Initialize all required subscriptions"""
        # Subscribe to OHLC data
        await self.connection.add_subscription(
            sub_key=f"ohlc-{self.pair}",
            channel=ChannelType.OHLC,
            symbols=[self.pair],
            callback=self.on_ohlc_data,
            interval=self.interval_minutes
        )

        # Subscribe to order book
        await self.connection.add_subscription(
            sub_key=f"book-{self.pair}",
            channel=ChannelType.BOOK,
            symbols=[self.pair],
            callback=self.on_book_data,
            depth=self.book_depth
        )

    # Data processing methods (to be implemented)
    async def initialize_historical_data(self):
        """
        Fetch and store historical minute-level OHLC data from Kraken since the last stored timestamp.
        Then resample to hourly and enrich with EMAs.
        """
        # Get the last timestamp in the database
        query = "SELECT MAX(timestamp) FROM kraken_ohlc WHERE pair = %s"
        result = self.db.execute_select(query, (self.pair,))
        last_timestamp = result[0][0] if result and result[0][0] else (
            datetime.now(timezone.utc) - timedelta(days=30))
        since = int(last_timestamp.timestamp())

        while True:
            logger.info(
                f"Fetching OHLC data since {datetime.fromtimestamp(since, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
            # Fetch historical OHLC data (minute-level) from Kraken
            ohlc_data = self.api.get_ohlc(
                self.pair, interval=self.interval_minutes, since=since)

            if not ohlc_data:
                logger.info("No more historical data available.")
                break  # Exit loop when no data is returned

            # Process and store the data
            new_since = None

            for candle in ohlc_data['result'][self.pair]:
                timestamp = datetime.fromtimestamp(candle[0], tz=timezone.utc)
                open_price, high_price, low_price, close_price, _, volume = map(
                    float, candle[1:7])

                query = """
                INSERT INTO kraken_ohlc (timestamp, pair, open_price, high_price, low_price, close_price, volume)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (timestamp, pair) DO NOTHING
                """
                self.db.execute_insert(
                    query, (timestamp, self.pair, open_price, high_price, low_price, close_price, volume))

                new_since = max(new_since or since, int(candle[0]))

            if new_since is None or new_since == since:
                break

            since = new_since
            await asyncio.sleep(3)  # Rate limiting

        # Process historical candles for strategy initialization
        await self._process_historical_candles()

    async def _process_historical_candles(self):
        """Process historical candles to initialize EMA values"""
        lookback_period = datetime.now(timezone.utc) - timedelta(hours=800)
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp >= %s
        ORDER BY timestamp ASC
        """
        float_columns = list(range(1, 6))
        historical_candles = self.db.execute_select(
            query, (self.pair, lookback_period), float_columns=float_columns)

        if historical_candles:
            df = pd.DataFrame(historical_candles, columns=[
                'timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'])

            # Preprocess data using strategy
            if hasattr(self.strategy, 'preprocess_data'):
                df = self.strategy.preprocess_data(df)

                if not df.empty:
                    # Only keep the most recent candles if there are too many
                    if len(df) > self.max_queue_size:
                        logger.warning(
                            f"Too many historical candles ({len(df)}), keeping only the most recent {self.max_queue_size}")
                        df = df.iloc[-self.max_queue_size:]

                    logger.info(
                        f"Processed {len(df)} historical candles, queue size: {self.candle_queue.qsize()}/{self.max_queue_size}")

                    # Store the last processed candle time
                    if 'timestamp' in df.columns and not df.empty:
                        self.last_start_time = df['timestamp'].iloc[-1]

    # Message handlers
    def on_ohlc_data(self, ohlc_message):
        """Handle incoming OHLC data"""
        asyncio.create_task(self._process_ohlc_data_safe(ohlc_message))

    async def _process_ohlc_data_safe(self, ohlc_message):
        """Thread-safe OHLC data processing"""
        async with self._processing_lock:
            await self._process_ohlc_data(ohlc_message)

    async def _process_ohlc_data(self, ohlc_data: dict):
        """Process OHLC data with v2 format handling"""
        try:
            if ohlc_data.get("channel") == "ohlc":
                if "success" in ohlc_data and not ohlc_data["success"]:
                    logger.error(
                        f"OHLC subscription error: {ohlc_data.get('error', 'Unknown error')}")
                    return

                ohlc = ohlc_data.get("data", [])
                if not ohlc:
                    logger.warning("Received empty OHLC data")
                    return

                updates = [ohlc] if not isinstance(ohlc, list) else ohlc

                for ohlc_update in updates:
                    try:
                        symbol = ohlc_update.get('symbol', self.pair)
                        if symbol and symbol != self.pair:
                            continue

                        timestamp_value = ohlc_update.get(
                            'time') or ohlc_update.get('timestamp')
                        if not timestamp_value:
                            logger.warning(
                                f"No timestamp found in OHLC data: {ohlc_update}")
                            continue

                        try:
                            if isinstance(timestamp_value, (int, float)):
                                if timestamp_value > 1e12:  # Likely in milliseconds
                                    timestamp_value = timestamp_value / 1000.0
                                start_time = datetime.fromtimestamp(
                                    timestamp_value, tz=timezone.utc)
                            elif isinstance(timestamp_value, str):
                                if timestamp_value.endswith('Z'):
                                    timestamp_value = timestamp_value[:-
                                                                      1] + '+00:00'
                                start_time = datetime.fromisoformat(
                                    timestamp_value)
                                if start_time.tzinfo is None:
                                    start_time = start_time.replace(
                                        tzinfo=timezone.utc)
                            else:
                                logger.error(
                                    f"Unsupported timestamp format: {timestamp_value}")
                                continue

                        except (ValueError, TypeError) as e:
                            logger.error(
                                f"Error parsing timestamp {timestamp_value}: {e}")
                            continue

                        candle = {
                            'timestamp': start_time,
                            'open_price': float(ohlc_update.get('open', ohlc_update.get('open_price', 0))),
                            'high_price': float(ohlc_update.get('high', ohlc_update.get('high_price', 0))),
                            'low_price': float(ohlc_update.get('low', ohlc_update.get('low_price', 0))),
                            'close_price': float(ohlc_update.get('close', ohlc_update.get('close_price', 0))),
                            'volume': float(ohlc_update.get('volume', ohlc_update.get('volume_24h', 0)))
                        }

                        if candle.get('close_price') is None:
                            logger.warning(
                                f"Skipping candle with missing close_price: {candle}")
                            continue

                        await self._store_candle(candle)

                        if not hasattr(self, 'last_start_time') or start_time > self.last_start_time:
                            if hasattr(self, 'last_start_time'):
                                await self._process_complete_candle()
                            self.last_start_time = start_time

                    except Exception as e:
                        logger.error(
                            f"Error processing OHLC update {ohlc_update}: {e}", exc_info=True)
                        continue

            else:
                logger.warning(f"Received unexpected data format: {ohlc_data}")

        except Exception as e:
            logger.error(f"Error processing OHLC data: {e}", exc_info=True)

    def on_book_data(self, book_message):
        """Handle incoming order book data"""
        asyncio.create_task(self._process_book_data_safe(book_message))

    async def _process_book_data_safe(self, book_message):
        """Thread-safe order book processing"""
        async with self._processing_lock:
            await self._process_book_data(book_message)

    async def _process_book_data(self, book_message):
        """Process order book updates"""
        try:
            logger.debug(
                f"Processing order book data: {json.dumps(book_message, default=str)}")

            if not isinstance(book_message, dict):
                logger.warning(
                    f"Received non-dict order book data: {book_message}")
                return

            book_updates = []
            if 'data' in book_message and isinstance(book_message['data'], list):
                book_updates = book_message['data']
            elif 'bids' in book_message or 'asks' in book_message:
                book_updates = [book_message]
            else:
                logger.warning(
                    f"Unexpected order book message format: {book_message}")
                return

            timestamp = datetime.now(timezone.utc)
            book_type = book_message.get('type', 'update')

            for book_update in book_updates:
                if not isinstance(book_update, dict):
                    logger.warning(
                        f"Skipping invalid book update (not a dict): {book_update}")
                    continue

                symbol = (
                    book_update.get('symbol') or
                    book_message.get('symbol') or
                    book_update.get('pair') or
                    book_message.get('pair') or
                    self.pair
                )
                if not symbol:
                    logger.warning(
                        f"No symbol found in order book update: {book_update}")
                    continue

                bids = book_update.get('bids', [])
                asks = book_update.get('asks', [])
                checksum = book_update.get('checksum')

                update_type = book_update.get('type')
                if update_type is None:
                    if 'bids' in book_update and 'asks' in book_update and len(book_update['bids']) > 10:
                        update_type = 'snapshot'
                    else:
                        update_type = 'update'

                def process_levels(levels, is_bid=False):
                    if not levels:
                        return []
                    if isinstance(levels[0], (list, tuple)):
                        return [{'price': float(p[0]), 'qty': float(p[1]), 'timestamp': p[2] if len(p) > 2 else None, 'is_bid': is_bid}
                                for p in levels]
                    if isinstance(levels[0], dict):
                        for level in levels:
                            level['is_bid'] = is_bid
                    return levels

                processed_bids = process_levels(bids, True)
                processed_asks = process_levels(asks, False)

                if update_type == 'snapshot':
                    self.order_book['bids'] = {
                        level['price']: level for level in processed_bids}
                    self.order_book['asks'] = {
                        level['price']: level for level in processed_asks}
                    logger.debug(
                        f"Reset order book with {len(processed_bids)} bids and {len(processed_asks)} asks")
                else:
                    for level in processed_bids:
                        if level['qty'] == 0:
                            self.order_book['bids'].pop(level['price'], None)
                        else:
                            self.order_book['bids'][level['price']] = level

                    for level in processed_asks:
                        if level['qty'] == 0:
                            self.order_book['asks'].pop(level['price'], None)
                        else:
                            self.order_book['asks'][level['price']] = level
                    logger.debug(
                        f"Updated order book: {len(processed_bids)} bid updates, {len(processed_asks)} ask updates")

                # Optionally store order book snapshots
                if update_type == 'snapshot':
                    bids = sorted(
                        self.order_book['bids'].values(), key=lambda x: -x['price'])
                    asks = sorted(
                        self.order_book['asks'].values(), key=lambda x: x['price'])

                    now = datetime.now(timezone.utc)
                    minutes_to_hour = 60 - now.minute

                    if minutes_to_hour <= 5:  # Only store near the hour mark
                        bids_json = json.dumps(bids) if bids else '[]'
                        asks_json = json.dumps(asks) if asks else '[]'

                        try:
                            insert_query = """
                            INSERT INTO order_book_snapshots
                                (pair, snapshot_time, bids, asks, checksum)
                            VALUES (%s, %s, %s, %s, %s)
                            """

                            self.db.execute_insert(
                                insert_query,
                                (
                                    symbol,
                                    timestamp,
                                    bids_json,
                                    asks_json,
                                    checksum
                                )
                            )
                            logger.debug(
                                f"Saved order book for {symbol} at {timestamp} with {len(bids)} bids and {len(asks)} asks")
                        except Exception as e:
                            logger.error(
                                f"Error saving order book snapshot: {e}")

        except Exception as e:
            logger.error(
                f"Error processing order book data: {e}", exc_info=True)

    # Data storage methods
    async def _store_candle(self, candle):
        """Store a candle in the database"""
        query = """
        INSERT INTO kraken_ohlc (timestamp, pair, open_price, high_price, low_price, close_price, volume)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (timestamp, pair) DO UPDATE
        SET open_price = EXCLUDED.open_price,
            high_price = EXCLUDED.high_price,
            low_price = EXCLUDED.low_price,
            close_price = EXCLUDED.close_price,
            volume = EXCLUDED.volume
        """
        try:
            self.db.execute_insert(query, (
                candle['timestamp'], self.pair, candle['open_price'],
                candle['high_price'], candle['low_price'], candle['close_price'], candle['volume']
            ))
            logger.debug(
                f"Upserted candle for {candle['timestamp']} into database")

            # Check if we need to retrain PPO model
            if hasattr(self.strategy, '__class__') and self.strategy.__class__.__name__ == 'PPOStrategy':
                await self._check_ppo_training_schedule(candle['timestamp'])

        except Exception as e:
            logger.error(
                f"Failed to upsert candle for {candle['timestamp']}: {e}")
            raise

    # Strategy integration
    async def _process_complete_candle(self):
        """Process a complete candle for strategy execution"""
        try:
            # Get the most recent candle
            query = """
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM kraken_ohlc
            WHERE pair = %s
            ORDER BY timestamp DESC
            LIMIT 1
            """
            result = self.db.execute_select(query, (self.pair,))

            if result:
                candle = {
                    'timestamp': result[0][0],
                    'open_price': float(result[0][1]),
                    'high_price': float(result[0][2]),
                    'low_price': float(result[0][3]),
                    'close_price': float(result[0][4]),
                    'volume': float(result[0][5])
                }

                # Add to queue for any consumers
                try:
                    self.candle_queue.put_nowait(candle)
                except asyncio.QueueFull:
                    # Remove oldest candle if queue is full
                    try:
                        self.candle_queue.get_nowait()
                        self.candle_queue.task_done()
                        self.candle_queue.put_nowait(candle)
                    except Exception as e:
                        logger.error(f"Error managing candle queue: {e}")

        except Exception as e:
            logger.error(
                f"Error processing complete candle: {e}", exc_info=True)

    # PPO training integration
    async def _check_ppo_training_schedule(self, current_time: datetime):
        """Check if it's time to retrain the PPO model"""
        if not hasattr(self.strategy, '__class__') or self.strategy.__class__.__name__ != 'PPOStrategy':
            return

        from app.strategy.ppo_strategy import PPOStrategy

        if not isinstance(self.strategy, PPOStrategy):
            return

        ppo_strategy = self.strategy

        if not hasattr(ppo_strategy, 'last_training_time') or ppo_strategy.last_training_time is None:
            ppo_strategy.last_training_time = current_time
            logger.info(
                f"Initialized PPO strategy last_training_time to {current_time}")
            return

        days_since_training = (
            current_time - ppo_strategy.last_training_time).days
        retrain_interval = getattr(ppo_strategy, 'retrain_interval_days', 7)

        if days_since_training >= retrain_interval:
            logger.info(
                f"Retraining interval reached ({retrain_interval} days). Initiating PPO model retraining.")

            try:
                historical_data = await self._get_all_historical_data(end_time=current_time)

                if historical_data is not None and len(historical_data) > 0:
                    logger.info(
                        f"Training PPO model with {len(historical_data)} historical candles")
                    if hasattr(ppo_strategy, '_train_model') and callable(getattr(ppo_strategy, '_train_model')):
                        ppo_strategy._train_model(historical_data)

                        if hasattr(ppo_strategy, 'reset_historical_data') and callable(getattr(ppo_strategy, 'reset_historical_data')):
                            ppo_strategy.reset_historical_data(historical_data)

                        ppo_strategy.last_training_time = current_time
                        logger.info(
                            f"PPO model training complete. Next training scheduled in {retrain_interval} days.")
                    else:
                        logger.error(
                            "PPO strategy does not have a _train_model method")
                else:
                    logger.warning(
                        "No historical data available for PPO model training")
            except Exception as e:
                logger.error(f"Error during PPO model training: {e}")

    async def _get_all_historical_data(self, end_time: datetime) -> pd.DataFrame:
        """
        Retrieve all historical data from the database up to end_time.

        Args:
            end_time (datetime): The end time for the historical data query

        Returns:
            pd.DataFrame: Preprocessed historical data
        """
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp < %s
        ORDER BY timestamp ASC
        """
        try:
            logger.info(
                f"Retrieving all historical data for {self.pair} up to {end_time}")
            historical_data = self.db.execute_select(
                query,
                (self.pair, end_time),
                # Convert numeric columns to float
                float_columns=list(range(1, 6))
            )

            if not historical_data:
                logger.warning("No historical data found in database")
                return pd.DataFrame()

            df = pd.DataFrame(historical_data, columns=[
                'timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'
            ])

            # Preprocess data using strategy
            if hasattr(self.strategy, 'preprocess_data'):
                df = self.strategy.preprocess_data(df)

            logger.info(
                f"Retrieved and preprocessed {len(df)} candles from database")
            return df

        except Exception as e:
            logger.error(f"Error retrieving historical data: {e}")
            return pd.DataFrame()

    async def get_next_candle(self) -> Optional[pd.Series]:
        """
        Retrieve the next candle from the real-time queue.

        Returns:
            Optional[pd.Series]: The next candle as a pandas Series, or None if no candles are available.
        """
        try:
            # Get candle from queue with a timeout to avoid blocking indefinitely
            candle_dict = await asyncio.wait_for(self.candle_queue.get(), timeout=1.0)
            self.candle_queue.task_done()

            # Convert dict to pandas Series
            candle_series = pd.Series(candle_dict)
            return candle_series

        except asyncio.TimeoutError:
            # No candle available within timeout
            return None
        except Exception as e:
            logger.error(f"Error getting next candle: {e}")
            return None
